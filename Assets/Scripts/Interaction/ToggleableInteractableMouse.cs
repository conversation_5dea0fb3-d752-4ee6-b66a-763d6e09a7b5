using System.Collections.Generic;
using InGameComputer.InGameMouse;
using Services;
using UnityEngine;
using UnityEngine.InputSystem;

public class ToggleableInteractableGrabbableMouse : ToggleableInteractableGrabbable
{
    [SerializeField]
    public List<AudioClip> ClickSounds;
    
    [SerializeField]
    private float MouseSensitivity = 0.1f;

    [SerializeField]
    private float LiftEndedPositionOffset = 0.025f;

    #region Properties

    private PlayerInputActions _playerInputActions;
    private PlayerInputActions PlayerInputActions
    {
        get
        {
            if (_playerInputActions == null)
                _playerInputActions = ServiceLocator.Locate<PlayerInputActions>();
            
            return _playerInputActions;
        }
    }
    
    private SystemMouseInput _systemMouseInput;
    private SystemMouseInput SystemMouseInput
    {
        get
        {
            if (_systemMouseInput == null)
                _systemMouseInput = ServiceLocator.Locate<SystemMouseInput>();
            
            return _systemMouseInput;
        }
    }
    
    private MouseModule _mouseModule;

    private MouseModule MouseModule
    {
        get
        {
            if (_mouseModule == null)
                _mouseModule = ServiceLocator.Locate<MouseModule>();
            
            return _mouseModule;
        }
    }
    
    private CollisionSoundPlayer _collisionSoundPlayer;
    private CollisionSoundPlayer CollisionSoundPlayer
    {
        get
        {
            if (_collisionSoundPlayer == null)
                _collisionSoundPlayer = GetComponent<CollisionSoundPlayer>();

            return _collisionSoundPlayer;
        }
    }
    
    private Rigidbody Rigidbody
    {
        get
        {
            if (_Rigidbody == null)
                _Rigidbody = GetComponent<Rigidbody>();

            return _Rigidbody;
        }
    }

    private AudioSource _audioSource;
    private AudioSource AudioSource
    {
        get
        {
            if (_audioSource == null)
                _audioSource = GetComponent<AudioSource>();

            return _audioSource;
        }
    }
    
    #endregion

    private void Start()
    {
        PlayerInputActions.Mouse.Disable();
        
        // PlayerInputActions.Mouse.MouseMovement.performed += OnMouseMovement;
        PlayerInputActions.Mouse.Lift.started += OnLiftStarted;
        PlayerInputActions.Mouse.Lift.canceled += OnLiftCanceled;
        PlayerInputActions.Mouse.Click.performed += OnClickPerformed;
        PlayerInputActions.Mouse.Exit.performed += OnExitPerformed;
    }

    private void OnDestroy()
    {
        PlayerInputActions.Mouse.MouseMovement.performed -= OnMouseMovement;
        PlayerInputActions.Mouse.Lift.started -= OnLiftStarted;
        PlayerInputActions.Mouse.Lift.canceled -= OnLiftCanceled;
        PlayerInputActions.Mouse.Click.performed -= OnClickPerformed;
        PlayerInputActions.Mouse.Exit.performed -= OnExitPerformed;
    }

    #region InputActions Callbacks

    private void OnMouseMovement(InputAction.CallbackContext ctx)
    {
        var mouseMovement = ctx.ReadValue<Vector2>();
        
        // map mouse movement to local coordinates (x is forward, y is right)
        mouseMovement = transform.InverseTransformDirection(mouseMovement);
        
        Rigidbody.MovePosition(Rigidbody.position + new Vector3(mouseMovement.x, 0, mouseMovement.y) * MouseSensitivity);
    }

    private void OnLiftStarted(InputAction.CallbackContext ctx)
    {
        var hit = RaycastDown();
        if (hit.collider != null)
        {
            Rigidbody.MovePosition(hit.point + transform.up * 0.05f);
        }
    }

    private void OnLiftCanceled(InputAction.CallbackContext ctx)
    {
        var hit = RaycastDown();
        if (hit.collider != null)
        {
            Rigidbody.MovePosition(hit.point + transform.up * LiftEndedPositionOffset);
        }

        CollisionSoundPlayer.PlayRandomCollisionSound();
    }

    private void OnClickPerformed(InputAction.CallbackContext ctx)
    {
        SystemMouseInput.OnClick();
        PlayRandomClick();
    }

    private void OnExitPerformed(InputAction.CallbackContext ctx)
    {
        EndInteraction();
    }

    #endregion

    private void PlayRandomClick()
    {
        // play random clip from ClickSounds with a pitch shift
        if (ClickSounds.Count > 0)
        {
            var randomClip = ClickSounds[Random.Range(0, ClickSounds.Count)];
            var randomPitch = Random.Range(0.9f, 1.1f);
            AudioSource.volume = 1.0f;
            AudioSource.pitch = randomPitch;
            AudioSource.PlayOneShot(randomClip);
        }
    }

    private RaycastHit RaycastDown()
    {
        var ray = new Ray(transform.position, -transform.up);
        Physics.Raycast(ray, out var hit);
        return hit;
    }

    protected override void OnInteract()
    {
        base.OnInteract();
        
        MouseModule.EnterMouseMode();
        
        Rigidbody.isKinematic = true;
        Rigidbody.MovePosition(Rigidbody.position + transform.up * LiftEndedPositionOffset);
    }

    protected override void OnEndInteraction()
    {
        base.OnEndInteraction();
        
        MouseModule.ExitMouseMode();
        
        Rigidbody.isKinematic = false;
    }
}
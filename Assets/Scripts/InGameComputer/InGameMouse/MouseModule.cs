using Data;
using Services;
using UnityEngine;
using UnityEngine.InputSystem;

namespace InGameComputer.InGameMouse
{
    public class MouseModule : MonoBehaviour
    {
        [SerializeField] private RangedVector2Variable MousePosition;
        [SerializeField] private float MouseSensitivity = 6800f;
        [SerializeField] private float MouseModeSensitivity = 50f;
        [SerializeField] private float DetectionDistance = 0.05f;
    
        private Vector3 _lastMousePosition;
        private Vector3 _currentMousePosition;
        private bool _isInMouseMode;

        private PlayerInputActions _playerInputActions;
        private PlayerInputActions PlayerInputActions
        {
            get
            {
                if (_playerInputActions == null)
                    _playerInputActions = ServiceLocator.Locate<PlayerInputActions>();
            
                return _playerInputActions;
            }
        }
    
        private StarterAssetsInputs _starterAssetsInputs;
        private StarterAssetsInputs StarterAssetsInputs
        {
            get
            {
                if (_starterAssetsInputs == null)
                    _starterAssetsInputs = ServiceLocator.Locate<StarterAssetsInputs>();
            
                return _starterAssetsInputs;
            }
        }

        private CinemachineBlender _cinemachineBlender;

        private CinemachineBlender CinemachineBlender
        {
            get
            {
                if (_cinemachineBlender == null)
                    _cinemachineBlender = ServiceLocator.Locate<CinemachineBlender>();
                
                return _cinemachineBlender;
            }
        }

        private void Awake()
        {
            ServiceLocator.Register(this);
        }

        private void Start()
        {
            PlayerInputActions.Mouse.MouseMovement.performed += MouseMovementOnPerformed;
        }

        private void Update()
        {
            if (_isInMouseMode)
            {
                return;
            }
            
            Vector3 raycastStartOffset = transform.up * DetectionDistance/2;
            Ray ray = new Ray(transform.position + raycastStartOffset, -transform.up);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, DetectionDistance))
            {
                if (_lastMousePosition == Vector3.zero)
                    _lastMousePosition = hit.point;
                
                UpdatePhysicsMousePosition(hit.point);
            }
            else
            {
                _lastMousePosition = Vector3.zero;
            }
        }
        
        public void EnterMouseMode()
        {
            PlayerInputActions.Player.Disable();
            PlayerInputActions.Mouse.Enable();
        
            _isInMouseMode = true;
        
            CinemachineBlender.SwitchToCameraB();
            
            StarterAssetsInputs.CanMove = false;
            StarterAssetsInputs.CanLook = false;
        }
        
        public void ExitMouseMode()
        {
            PlayerInputActions.Mouse.Disable();
            PlayerInputActions.Player.Enable();
        
            _isInMouseMode = false;
        
            CinemachineBlender.SwitchToCameraA();
            
            StarterAssetsInputs.CanMove = true;
            StarterAssetsInputs.CanLook = true;
        }

        private void MouseMovementOnPerformed(InputAction.CallbackContext ctx)
        {
            if (!_isInMouseMode)
            {
                return;
            }
            
            var mouseDelta = ctx.ReadValue<Vector2>();
            MousePosition.Value += mouseDelta * MouseModeSensitivity;
        }

        private void UpdatePhysicsMousePosition(Vector3 hitPoint)
        {
            _currentMousePosition = hitPoint;

            if (Mathf.Approximately(_currentMousePosition.sqrMagnitude, _lastMousePosition.sqrMagnitude))
                return;
        
            // add the difference to the mouse position
            var mouseDelta = (_currentMousePosition - _lastMousePosition) * MouseSensitivity;
            var mouseDelta2D = new Vector2(mouseDelta.x, mouseDelta.z);
            
            // translate to local coordinates
            mouseDelta2D = transform.InverseTransformDirection(mouseDelta2D);
        
            MousePosition.Value += mouseDelta2D;
        
            _lastMousePosition = _currentMousePosition;
        }
    }
}

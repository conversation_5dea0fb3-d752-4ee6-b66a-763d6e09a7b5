using System.Linq;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace InGameComputer.InGameMouse
{
    public abstract class VirtualMouseInput : MonoBehaviour
    {
        private void Start()
        {
            Hide();
        }
        
        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            gameObject.SetActive(false);
        }

        public void OnClick()
        {
            RectTransform mouseTransform = (RectTransform) transform;

            ClickOnUIElement(mouseTransform);
        }

        private void ClickOnUIElement(RectTransform mouseTransform)
        {
            // get a list of all UI elements under the same parent and sort them by top-most to bottom-most
            var uiElements = mouseTransform.parent.GetComponentsInChildren<RectTransform>(false).Reverse().ToArray();
        
            // send a click event to the first button found at the current position of the virtual mouse
            foreach (var uiElement in uiElements)
            {
                if (uiElement == mouseTransform)
                    continue;
            
                if (uiElement.GetComponent<IPointerClickHandler>() == null)
                    continue;
            
                // check if the mouse is over the UI element
                if (!RectTransformUtility.RectangleContainsScreenPoint(uiElement, mouseTransform.position))
                    continue;
            
                var button = uiElement.GetComponentInParent<Button>();

                if (!button)
                    continue;
            
                // invoke the click event of the UI Button element
                button.onClick.Invoke();
                break;
            }
        }
    }
}
using System;
using InGameComputer.InGameMouse;
using Services;
using UnityEngine;

public class ComputerMouseService : MonoBehaviour
{
    private SystemMouseInput _systemMouseInput;
    
    private void Awake()
    {
        ServiceLocator.Register(this);
        _systemMouseInput = GetComponent<SystemMouseInput>();
    }

    private void Start()
    {
        Enable();
    }

    public void Enable()
    {
        _systemMouseInput.enabled = true;
        Show();
    }
    
    public void Disable()
    {
        _systemMouseInput.enabled = false;
        Hide();
    }

    private void Show()
    {
        _systemMouseInput.gameObject.SetActive(true);
    }

    private void Hide()
    {
        _systemMouseInput.gameObject.SetActive(false);
    }
}

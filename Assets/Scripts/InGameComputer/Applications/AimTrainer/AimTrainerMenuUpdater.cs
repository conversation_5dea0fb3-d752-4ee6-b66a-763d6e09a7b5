using InGameComputer.Applications.AimTrainer;
using TMPro;
using UnityEngine;

public class AimTrainerMenuUpdater : MonoBehaviour
{
    private App _app;
    private AimTrainer _aimTrainer;
    [SerializeField] private TextMeshProUGUI ScoreText;
    [SerializeField] private TextMeshProUGUI HighScoreText;
    
    private string _scorePrefix = "Score: ";
    private string _highScorePrefix = "High Score: ";
    
    private void Start()
    {
        _app = GetComponentInParent<CustomMouseApp>();
        _aimTrainer = _app.Applet as AimTrainer;
        
        _app.ApplicationStarted += UpdateScore;
        _aimTrainer.OnMenuOpened += UpdateScore;
    }
    
    private void OnDestroy()
    {
        _app.ApplicationStarted -= UpdateScore;
        _aimTrainer.OnMenuOpened -= UpdateScore;
    }

    private void UpdateScore()
    {
        var aimTrainerScore = _aimTrainer.LastScore;
        
        ScoreText.gameObject.SetActive(aimTrainerScore > 0);
        ScoreText.text = _scorePrefix + aimTrainerScore;
        
        var currentHighScore = PlayerPrefs.GetInt("AimTrainerHighScore", 0);
        if (aimTrainerScore > currentHighScore)
        {
            PlayerPrefs.SetInt("AimTrainerHighScore", aimTrainerScore);
            currentHighScore = aimTrainerScore;
        }
        
        HighScoreText.text = _highScorePrefix + currentHighScore;
    }
}

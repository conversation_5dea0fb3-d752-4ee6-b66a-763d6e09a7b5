using InGameComputer.InGameMouse;
using Services;
using UnityEngine;

public class CustomMouseApp : App
{
    [SerializeField]
    private VirtualMouseInput VirtualMouseInput;
    
    ComputerMouseService _computerMouseService;

    private ComputerMouseService ComputerMouseService
    {
        get
        {
            if (_computerMouseService == null)
                _computerMouseService = ServiceLocator.Locate<ComputerMouseService>();
            
            return _computerMouseService;
        }
    }
    
    protected override void SetupApplet()
    {
        base.SetupApplet();
        
        VirtualMouseInput.enabled = false;
    }

    protected override void OnOpened()
    {
        base.OnOpened();
        
        ComputerMouseService.Disable();
        VirtualMouseInput.enabled = true;
    }

    protected override void OnMinimized()
    {
        base.OnMinimized();
        
        ComputerMouseService.Enable();
        VirtualMouseInput.enabled = false;
    }
}